import {
  _CONTACT_CHANNEL_OPTIONS,
  _FOLLOW_UP_STATUS_OPTIONS,
  _OPPORTUNITY_OPTIONS,
  _SERVICE_OPTIONS,
} from "@components/badge";
import { Button, Input, Select, Textarea } from "@components/common";
import { useForm } from "@tanstack/react-form";
import { useTranslation } from "react-i18next";
import type { FormValues } from "./interface";

export const AddLeadForm = () => {
  const { t } = useTranslation();

  const today = new Date().toISOString().split("T")[0];

  const form = useForm({
    defaultValues: {
      contactChannel: "",
      contactInfo: "",
      followUpDate: "",
      followUpStatus: "",
      name: "",
      note: "",
      opportunity: "",
      servicesOfInterest: "",
      startDate: today,
    },
    onSubmit: async ({ value }: { value: FormValues }) => {
      alert(JSON.stringify(value, null, 2));
    },
  });

  return (
    <form
      className="flex h-full flex-col gap-6 overflow-hidden p-1"
      onSubmit={(e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        e.stopPropagation();
        form.handleSubmit();
      }}
    >
      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          {/* Name Field */}
          <form.Field
            name="name"
            validators={{
              onChange: ({ value }: { value: string }) =>
                !value ? `${t("addLead.name")} is required` : undefined,
            }}
          >
            {(fieldApi) => (
              <div className="group col-span-2 grid grid-cols-3">
                <label htmlFor={fieldApi.name} className="flex gap-1 text-h6">
                  <span className="text-h6">{t("addLead.name")}</span>
                  <span className="text-error text-h6">*</span>
                </label>
                <Input
                  id={fieldApi.name}
                  type="text"
                  placeholder={t("addLead.name")}
                  value={fieldApi.state.value}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  onBlur={fieldApi.handleBlur}
                  className={`col-span-2 flex-1 group-hover:bg-base-200 ${
                    fieldApi.state.meta.errors.length > 0 ? "border-error" : ""
                  }`}
                  variant="transparent"
                />
                {fieldApi.state.meta.errors.length > 0 && (
                  <div className="col-span-full text-error text-sm">
                    {fieldApi.state.meta.errors[0]}
                  </div>
                )}
              </div>
            )}
          </form.Field>

          {/* Opportunity Field */}
          <form.Field name="opportunity">
            {(fieldApi) => (
              <div className="col-span-1 grid grid-cols-2">
                <label htmlFor={fieldApi.name} className="text-h6">
                  {t("addLead.opportunity")}
                </label>
                <div>
                  <Select
                    id={fieldApi.name}
                    options={_OPPORTUNITY_OPTIONS}
                    size="sm"
                    variant="popup"
                    value={fieldApi.state.value}
                    onChange={(value) => fieldApi.handleChange(value)}
                    className={
                      fieldApi.state.meta.errors.length > 0
                        ? "border-error"
                        : ""
                    }
                    placeholder={t("addLead.opportunity")}
                  />
                  {fieldApi.state.meta.errors.length > 0 && (
                    <div className="text-error text-sm">
                      {fieldApi.state.meta.errors[0]}
                    </div>
                  )}
                </div>
              </div>
            )}
          </form.Field>

          {/* Follow Up Status Field */}
          <form.Field name="followUpStatus">
            {(fieldApi) => (
              <div className="col-span-2 grid grid-cols-3">
                <label htmlFor={fieldApi.name} className="text-h6">
                  {t("addLead.followUpStatus")}
                </label>
                <div className="col-span-2">
                  <Select
                    id={fieldApi.name}
                    options={_FOLLOW_UP_STATUS_OPTIONS}
                    size="sm"
                    variant="popup"
                    value={fieldApi.state.value}
                    onChange={(value) => fieldApi.handleChange(value)}
                    className={`flex-1 ${
                      fieldApi.state.meta.errors.length > 0
                        ? "border-error"
                        : ""
                    }`}
                    placeholder={t("addLead.followUpStatus")}
                  />
                  {fieldApi.state.meta.errors.length > 0 && (
                    <div className="col-span-full text-error text-sm">
                      {fieldApi.state.meta.errors[0]}
                    </div>
                  )}
                </div>
              </div>
            )}
          </form.Field>

          {/* Contact Channel Field */}
          <form.Field
            name="contactChannel"
            validators={{
              onChange: ({ value }: { value: string }) =>
                !value
                  ? `${t("addLead.contactChannel")} is required`
                  : undefined,
            }}
          >
            {(fieldApi) => (
              <div className="col-span-1 grid grid-cols-2">
                <label htmlFor={fieldApi.name} className="flex gap-1 text-h6">
                  <span className="text-h6">{t("addLead.contactChannel")}</span>
                  <span className="text-error text-h6">*</span>
                </label>
                <div>
                  <Select
                    id={fieldApi.name}
                    options={_CONTACT_CHANNEL_OPTIONS}
                    size="sm"
                    variant="popup"
                    value={fieldApi.state.value}
                    onChange={(value) => fieldApi.handleChange(value)}
                    className={
                      fieldApi.state.meta.errors.length > 0
                        ? "border-error"
                        : ""
                    }
                    placeholder={t("addLead.contactChannel")}
                  />
                  {fieldApi.state.meta.errors.length > 0 && (
                    <div className="text-error text-sm">
                      {fieldApi.state.meta.errors[0]}
                    </div>
                  )}
                </div>
              </div>
            )}
          </form.Field>

          {/* Services of Interest Field */}
          <form.Field name="servicesOfInterest">
            {(fieldApi) => (
              <div className="col-span-2 grid grid-cols-3">
                <label htmlFor={fieldApi.name} className="text-h6">
                  {t("addLead.servicesOfInterest")}
                </label>
                <div className="col-span-2">
                  <Select
                    id={fieldApi.name}
                    options={_SERVICE_OPTIONS}
                    size="sm"
                    variant="popup"
                    value={fieldApi.state.value}
                    onChange={(value) => fieldApi.handleChange(value)}
                    className={`flex-1 ${
                      fieldApi.state.meta.errors.length > 0
                        ? "border-error"
                        : ""
                    }`}
                    placeholder={t("addLead.servicesOfInterest")}
                  />
                  {fieldApi.state.meta.errors.length > 0 && (
                    <div className="col-span-full text-error text-sm">
                      {fieldApi.state.meta.errors[0]}
                    </div>
                  )}
                </div>
              </div>
            )}
          </form.Field>

          {/* Start Date Field */}
          <form.Field name="startDate">
            {(fieldApi) => (
              <div className="col-span-1 grid grid-cols-2">
                <label htmlFor={fieldApi.name} className="text-h6">
                  {t("addLead.startDate")}
                </label>
                <div className="relative">
                  <Input
                    id={fieldApi.name}
                    type="date"
                    value={fieldApi.state.value}
                    onChange={(e) => fieldApi.handleChange(e.target.value)}
                    onBlur={fieldApi.handleBlur}
                    className="w-full"
                  />
                </div>
              </div>
            )}
          </form.Field>

          {/* Contact Info Field */}
          <form.Field name="contactInfo">
            {(fieldApi) => (
              <div className="group col-span-2 grid grid-cols-3">
                <label htmlFor={fieldApi.name} className="text-h6">
                  {t("addLead.contactInfo")}
                </label>
                <Input
                  id={fieldApi.name}
                  type="text"
                  placeholder={t("addLead.contactInfo")}
                  value={fieldApi.state.value}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  onBlur={fieldApi.handleBlur}
                  className={`col-span-2 flex-1 group-hover:bg-base-200 ${
                    fieldApi.state.meta.errors.length > 0 ? "border-error" : ""
                  }`}
                  variant="transparent"
                />
                {fieldApi.state.meta.errors.length > 0 && (
                  <div className="col-span-full text-error text-sm">
                    {fieldApi.state.meta.errors[0]}
                  </div>
                )}
              </div>
            )}
          </form.Field>

          {/* Follow Up Date Field */}
          <form.Field name="followUpDate">
            {(fieldApi) => (
              <div className="col-span-1 grid grid-cols-2">
                <label htmlFor={fieldApi.name} className="text-h6">
                  {t("addLead.followUpDate")}
                </label>
                <Input
                  id={fieldApi.name}
                  type="text"
                  value={fieldApi.state.value}
                  onChange={(e) => fieldApi.handleChange(e.target.value)}
                  onBlur={fieldApi.handleBlur}
                  disabled={true}
                  className="w-full"
                />
              </div>
            )}
          </form.Field>
        </div>
      </div>

      {/* บันทึก */}
      <form.Field name="note">
        {(fieldApi) => (
          <div className="flex min-h-0 flex-1 flex-col gap-2">
            <label htmlFor={fieldApi.name} className="text-h6">
              {t("addLead.note")}
            </label>
            <Textarea
              id={fieldApi.name}
              placeholder={t("addLead.note")}
              value={fieldApi.state.value}
              onChange={(e) => fieldApi.handleChange(e.target.value)}
              onBlur={fieldApi.handleBlur}
              className="min-h-[120px] flex-1 resize-none"
            />
            {fieldApi.state.meta.errors.length > 0 && (
              <div className="text-error text-sm">
                {fieldApi.state.meta.errors[0]}
              </div>
            )}
          </div>
        )}
      </form.Field>

      {/* Debug Info */}
      <form.Subscribe
        selector={(state) => [state.values, state.errors, state.canSubmit]}
      >
        {([values, errors, canSubmit]) => (
          <div className="rounded bg-gray-100 p-2 text-gray-500 text-xs">
            <div>Can Submit: {canSubmit ? "Yes" : "No"}</div>
            <div>Errors: {JSON.stringify(errors)}</div>
            <div>Values: {JSON.stringify(values)}</div>
          </div>
        )}
      </form.Subscribe>

      {/* Buttons */}
      <div className="flex w-full justify-end gap-3 pt-4">
        <Button variant="outline" className="w-28" type="button">
          {t("common.draft")}
        </Button>
        <form.Subscribe
          selector={(state) => [state.canSubmit, state.isSubmitting]}
        >
          {([canSubmit, isSubmitting]) => (
            <Button
              className="w-28"
              type="submit"
              disabled={!canSubmit || isSubmitting}
            >
              {isSubmitting ? t("common.loading") : t("common.save")}
            </Button>
          )}
        </form.Subscribe>
      </div>
    </form>
  );
};
